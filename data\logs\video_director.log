2025-07-21 14:54:28,980 - src.services.video_service - INFO - _validate_ffmpeg:33 - FFmpeg validation successful
2025-07-21 14:54:28,980 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 14:54:28,980 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 14:54:28,980 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 14:54:28,981 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 14:54:28,981 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 14:54:28,991 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 14:54:28,991 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 14:54:28,991 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 14:54:28,993 - src.workflow.engine - INFO - process_video:74 - 开始处理视频项目: Test_.\data\input\xierdatest.mp4
2025-07-21 14:54:28,993 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 14:54:28,995 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 14:54:28,995 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 14:54:28,995 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 14:54:28,995 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 14:54:28,995 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 14:54:28,996 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 14:54:28,996 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 14:54:28,996 - src.services.file_service - INFO - create_project_workspace:72 - Created workspace for project 853d9d99-bfd2-478d-a26c-3d8ccc3996ca
2025-07-21 14:54:28,997 - src.workflow.engine - INFO - process_video:98 - 阶段1: 开始视频分析
2025-07-21 14:54:28,998 - src.services.ai_service - INFO - analyze_video:48 - 开始分析视频: data\input\data\input\xierdatest.mp4
2025-07-21 14:54:28,998 - src.services.ai_service - INFO - analyze_video:87 - 视频分析完成
2025-07-21 14:54:28,999 - src.workflow.engine - INFO - process_video:114 - 阶段2: 开始视频拆条
2025-07-21 14:54:28,999 - src.services.video_service - INFO - split_video:90 - 开始拆分视频: data\input\data\input\xierdatest.mp4 -> 3 个片段
2025-07-21 14:54:28,999 - src.services.video_service - INFO - get_video_metadata:49 - 获取视频元数据: data\input\data\input\xierdatest.mp4
2025-07-21 14:54:29,000 - src.services.video_service - INFO - get_video_metadata:62 - 视频元数据获取成功: 1920x1080, 30.0s
2025-07-21 14:54:29,000 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 1: 00:00:00.000 -> 8.5s
2025-07-21 14:54:29,000 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_001.mp4
2025-07-21 14:54:29,001 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 2: 00:00:08.500 -> 7.7s
2025-07-21 14:54:29,001 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_002.mp4
2025-07-21 14:54:29,001 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 3: 00:00:16.200 -> 8.8s
2025-07-21 14:54:29,002 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_003.mp4
2025-07-21 14:54:29,002 - src.services.video_service - INFO - split_video:132 - 视频拆分完成: 3 个片段
2025-07-21 14:54:29,003 - src.workflow.engine - INFO - process_video:130 - 阶段3: 开始旁白优化和语音合成
2025-07-21 14:54:29,003 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_001.mp4
2025-07-21 14:54:29,004 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 14:54:29,004 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_002.mp4
2025-07-21 14:54:29,004 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 14:54:29,004 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_003.mp4
2025-07-21 14:54:29,004 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 14:54:29,004 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,005 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_1_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,005 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_1_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,006 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,006 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_2_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,006 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_2_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,007 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,007 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_3_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,007 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_3_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,010 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,010 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,010 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,011 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,011 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,011 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,012 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,012 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,012 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 14:54:29,012 - src.services.tts_service - INFO - select_best_audio:253 - Scene 1: Selected audio with duration 1.00s (target: 8.50s, diff: 7.50s, score: 0.000)
2025-07-21 14:54:29,013 - src.services.tts_service - INFO - select_best_audio:253 - Scene 2: Selected audio with duration 1.00s (target: 7.70s, diff: 6.70s, score: 0.000)
2025-07-21 14:54:29,013 - src.services.tts_service - INFO - select_best_audio:253 - Scene 3: Selected audio with duration 1.00s (target: 8.80s, diff: 7.80s, score: 0.000)
2025-07-21 14:54:29,013 - src.workflow.engine - INFO - process_video:143 - 阶段4: 开始视频处理和音画同步
2025-07-21 14:54:29,014 - src.services.video_service - INFO - process_all_segments:438 - 开始处理 3 个视频片段
2025-07-21 14:54:29,014 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 1: 速度比例 8.500
2025-07-21 14:54:29,014 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_001.mp4 -> 8.5x
2025-07-21 14:54:29,015 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_1.mp4
2025-07-21 14:54:29,015 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_1.mp4 + data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_1.mp4
2025-07-21 14:54:29,016 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_1.mp4
2025-07-21 14:54:29,016 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_1.mp4
2025-07-21 14:54:29,017 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 2: 速度比例 7.700
2025-07-21 14:54:29,017 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_002.mp4 -> 7.7x
2025-07-21 14:54:29,018 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_2.mp4
2025-07-21 14:54:29,018 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_2.mp4 + data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_2.mp4
2025-07-21 14:54:29,020 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_2.mp4
2025-07-21 14:54:29,020 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_2.mp4
2025-07-21 14:54:29,020 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 3: 速度比例 8.800
2025-07-21 14:54:29,020 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\segments\segment_003.mp4 -> 8.8x
2025-07-21 14:54:29,021 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_3.mp4
2025-07-21 14:54:29,021 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\speed_adjusted_3.mp4 + data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_3.mp4
2025-07-21 14:54:29,027 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_3.mp4
2025-07-21 14:54:29,027 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\processing\final_segment_3.mp4
2025-07-21 14:54:29,027 - src.services.video_service - INFO - process_all_segments:475 - 所有视频片段处理完成: 3 个
2025-07-21 14:54:29,029 - src.workflow.engine - INFO - process_video:159 - 阶段5: 开始最终视频合成
2025-07-21 14:54:29,029 - src.services.video_service - INFO - create_final_video:501 - 创建最终视频: 3 个片段 -> data\output\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\Test_.\data\input\xierdatest.mp4.mp4
2025-07-21 14:54:29,029 - src.services.video_service - INFO - concatenate_videos:310 - 拼接视频: 3 个文件 -> data\output\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\Test_.\data\input\xierdatest.mp4.mp4
2025-07-21 14:54:29,031 - src.services.video_service - INFO - concatenate_videos:327 - 视频拼接完成: data\output\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\Test_.\data\input\xierdatest.mp4.mp4
2025-07-21 14:54:29,031 - src.services.video_service - INFO - create_final_video:509 - 最终视频创建完成: data\output\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\Test_.\data\input\xierdatest.mp4.mp4
2025-07-21 14:54:29,033 - src.workflow.engine - INFO - process_video:173 - 视频项目处理完成: data\output\853d9d99-bfd2-478d-a26c-3d8ccc3996ca\Test_.\data\input\xierdatest.mp4.mp4
