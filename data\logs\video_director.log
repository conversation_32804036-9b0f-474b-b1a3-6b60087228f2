2025-07-21 12:58:45,683 - test - INFO - test:94 - 测试日志消息
2025-07-21 13:28:11,731 - src.services.ai_service - INFO - analyze_video:48 - 开始分析视频: test_video.mp4
2025-07-21 13:28:11,731 - src.services.ai_service - INFO - analyze_video:87 - 视频分析完成
2025-07-21 13:28:11,737 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: test_segment.mp4
2025-07-21 13:28:11,737 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 13:30:01,766 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_1_v1_test_voice_id.mp3
2025-07-21 13:30:01,767 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_1_v2_test_voice_id.mp3
2025-07-21 13:30:01,777 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_1_v3_test_voice_id.mp3
2025-07-21 13:30:01,778 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v1_test_voice_id.mp3
2025-07-21 13:30:01,778 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v3_test_voice_id.mp3
2025-07-21 13:30:01,778 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v2_test_voice_id.mp3
2025-07-21 13:30:01,779 - src.services.tts_service - INFO - select_best_audio:253 - Scene 1: Selected audio with duration 1.00s (target: 5.00s, diff: 4.00s, score: 0.000)
2025-07-21 13:32:21,858 - src.services.video_service - INFO - _validate_ffmpeg:33 - FFmpeg validation successful
2025-07-21 13:32:21,861 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 1: 速度比例 1.133
2025-07-21 13:32:21,861 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: test_segment.mp4 -> 1.1333333333333333x
2025-07-21 13:32:21,861 - src.services.video_service - ERROR - adjust_video_speed:245 - Speed adjustment failed: [Errno 2] No such file or directory: 'test_segment.mp4'
2025-07-21 13:32:21,863 - src.utils.retry - WARNING - async_wrapper:45 - Function adjust_video_speed failed (attempt 1/2), retrying in 2.62s: Failed to adjust video speed: [Errno 2] No such file or directory: 'test_segment.mp4'
2025-07-21 13:32:24,494 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: test_segment.mp4 -> 1.1333333333333333x
2025-07-21 13:32:24,495 - src.services.video_service - ERROR - adjust_video_speed:245 - Speed adjustment failed: [Errno 2] No such file or directory: 'test_segment.mp4'
2025-07-21 13:32:24,495 - src.utils.retry - ERROR - async_wrapper:34 - Function adjust_video_speed failed after 2 attempts: Failed to adjust video speed: [Errno 2] No such file or directory: 'test_segment.mp4'
2025-07-21 13:32:24,495 - src.services.video_service - ERROR - process_video_segment:419 - Failed to process video segment 1: Failed to adjust video speed: [Errno 2] No such file or directory: 'test_segment.mp4'
2025-07-21 13:33:13,250 - src.services.video_service - INFO - _validate_ffmpeg:33 - FFmpeg validation successful
2025-07-21 13:33:13,252 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 1: 速度比例 1.133
2025-07-21 13:33:13,252 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\test_segment.mp4 -> 1.1333333333333333x
2025-07-21 13:33:13,258 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\processing\speed_adjusted_1.mp4
2025-07-21 13:33:13,258 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\processing\speed_adjusted_1.mp4 + data\temp\test_audio.mp3 -> data\temp\processing\final_segment_1.mp4
2025-07-21 13:33:13,266 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\processing\final_segment_1.mp4
2025-07-21 13:33:13,266 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\processing\final_segment_1.mp4
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 13:35:06,671 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 13:35:06,688 - src.services.file_service - INFO - create_project_workspace:72 - Created workspace for project test_project_123
2025-07-21 13:35:06,695 - src.services.file_service - ERROR - _save_manifest:238 - Failed to save manifest: Object of type datetime is not JSON serializable
2025-07-21 13:35:06,696 - src.services.file_service - DEBUG - register_file:263 - Registered file test_ad3a6177: data\temp\test_project_123\test_file.txt
2025-07-21 13:35:06,697 - src.services.file_service - ERROR - _save_manifest:238 - Failed to save manifest: Object of type datetime is not JSON serializable
2025-07-21 13:35:06,698 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 13:35:06,698 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 13:35:06,698 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 13:35:06,698 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 13:35:06,698 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 13:35:06,699 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 13:35:06,699 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 13:35:06,699 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 13:37:35,519 - src.services.video_service - INFO - _validate_ffmpeg:33 - FFmpeg validation successful
2025-07-21 13:37:35,521 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 13:37:35,521 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 13:37:35,521 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 13:37:35,521 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 13:37:35,522 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 13:37:35,522 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 13:37:35,522 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 13:37:35,523 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 13:37:35,524 - src.workflow.engine - INFO - process_video:74 - 开始处理视频项目: Test_test_input.mp4
2025-07-21 13:37:35,524 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 13:37:35,525 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 13:37:35,525 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 13:37:35,525 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 13:37:35,525 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 13:37:35,525 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 13:37:35,525 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 13:37:35,525 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 13:37:35,529 - src.services.file_service - INFO - create_project_workspace:72 - Created workspace for project 875fbd66-6e4c-4b7c-bab8-276181dc8284
2025-07-21 13:37:35,529 - src.workflow.engine - INFO - process_video:98 - 阶段1: 开始视频分析
2025-07-21 13:37:35,529 - src.services.ai_service - INFO - analyze_video:48 - 开始分析视频: data\input\test_input.mp4
2025-07-21 13:37:35,530 - src.services.ai_service - INFO - analyze_video:87 - 视频分析完成
2025-07-21 13:37:35,530 - src.workflow.engine - INFO - process_video:114 - 阶段2: 开始视频拆条
2025-07-21 13:37:35,531 - src.services.video_service - INFO - split_video:90 - 开始拆分视频: data\input\test_input.mp4 -> 3 个片段
2025-07-21 13:37:35,531 - src.services.video_service - INFO - get_video_metadata:49 - 获取视频元数据: data\input\test_input.mp4
2025-07-21 13:37:35,531 - src.services.video_service - INFO - get_video_metadata:62 - 视频元数据获取成功: 1920x1080, 30.0s
2025-07-21 13:37:35,531 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 1: 00:00:00.000 -> 8.5s
2025-07-21 13:37:35,531 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\segments\segment_001.mp4
2025-07-21 13:37:35,533 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 2: 00:00:08.500 -> 7.7s
2025-07-21 13:37:35,533 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\segments\segment_002.mp4
2025-07-21 13:37:35,533 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 3: 00:00:16.200 -> 8.8s
2025-07-21 13:37:35,533 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\segments\segment_003.mp4
2025-07-21 13:37:35,533 - src.services.video_service - INFO - split_video:132 - 视频拆分完成: 3 个片段
2025-07-21 13:37:35,535 - src.workflow.engine - INFO - process_video:130 - 阶段3: 开始旁白优化和语音合成
2025-07-21 13:37:35,535 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\segments\segment_001.mp4
2025-07-21 13:37:35,535 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 13:37:35,535 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\segments\segment_002.mp4
2025-07-21 13:37:35,535 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 13:37:35,535 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\segments\segment_003.mp4
2025-07-21 13:37:35,535 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 13:37:35,535 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,539 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_1_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,539 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_1_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,539 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,539 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_2_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,539 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_2_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,539 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,540 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_3_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,540 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_3_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,542 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,542 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,542 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,542 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,543 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,543 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,544 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,544 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,544 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:37:35,544 - src.services.tts_service - INFO - select_best_audio:253 - Scene 1: Selected audio with duration 1.00s (target: 8.50s, diff: 7.50s, score: 0.000)
2025-07-21 13:37:35,544 - src.services.tts_service - INFO - select_best_audio:253 - Scene 2: Selected audio with duration 1.00s (target: 7.70s, diff: 6.70s, score: 0.000)
2025-07-21 13:37:35,544 - src.services.tts_service - INFO - select_best_audio:253 - Scene 3: Selected audio with duration 1.00s (target: 8.80s, diff: 7.80s, score: 0.000)
2025-07-21 13:37:35,546 - src.workflow.engine - INFO - process_video:143 - 阶段4: 开始视频处理和音画同步
2025-07-21 13:37:35,546 - src.services.video_service - INFO - process_all_segments:438 - 开始处理 3 个视频片段
2025-07-21 13:37:35,547 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 1: 速度比例 8.500
2025-07-21 13:37:35,547 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\segments\segment_001.mp4 -> 8.5x
2025-07-21 13:37:35,553 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\speed_adjusted_1.mp4
2025-07-21 13:37:35,553 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\speed_adjusted_1.mp4 + data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\final_segment_1.mp4
2025-07-21 13:37:35,557 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\final_segment_1.mp4
2025-07-21 13:37:35,558 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\final_segment_1.mp4
2025-07-21 13:37:35,558 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 2: 速度比例 7.700
2025-07-21 13:37:35,558 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\segments\segment_002.mp4 -> 7.7x
2025-07-21 13:37:35,562 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\speed_adjusted_2.mp4
2025-07-21 13:37:35,562 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\speed_adjusted_2.mp4 + data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\final_segment_2.mp4
2025-07-21 13:37:35,564 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\final_segment_2.mp4
2025-07-21 13:37:35,567 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\final_segment_2.mp4
2025-07-21 13:37:35,567 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 3: 速度比例 8.800
2025-07-21 13:37:35,567 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\segments\segment_003.mp4 -> 8.8x
2025-07-21 13:37:35,569 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\speed_adjusted_3.mp4
2025-07-21 13:37:35,571 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\speed_adjusted_3.mp4 + data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\final_segment_3.mp4
2025-07-21 13:37:35,574 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\final_segment_3.mp4
2025-07-21 13:37:35,575 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\875fbd66-6e4c-4b7c-bab8-276181dc8284\processing\final_segment_3.mp4
2025-07-21 13:37:35,575 - src.services.video_service - INFO - process_all_segments:475 - 所有视频片段处理完成: 3 个
2025-07-21 13:37:35,576 - src.workflow.engine - INFO - process_video:159 - 阶段5: 开始最终视频合成
2025-07-21 13:37:35,576 - src.services.video_service - INFO - create_final_video:501 - 创建最终视频: 3 个片段 -> data\output\875fbd66-6e4c-4b7c-bab8-276181dc8284\Test_test_input.mp4.mp4
2025-07-21 13:37:35,576 - src.services.video_service - INFO - concatenate_videos:310 - 拼接视频: 3 个文件 -> data\output\875fbd66-6e4c-4b7c-bab8-276181dc8284\Test_test_input.mp4.mp4
2025-07-21 13:37:35,584 - src.services.video_service - INFO - concatenate_videos:327 - 视频拼接完成: data\output\875fbd66-6e4c-4b7c-bab8-276181dc8284\Test_test_input.mp4.mp4
2025-07-21 13:37:35,584 - src.services.video_service - INFO - create_final_video:509 - 最终视频创建完成: data\output\875fbd66-6e4c-4b7c-bab8-276181dc8284\Test_test_input.mp4.mp4
2025-07-21 13:37:35,585 - src.workflow.engine - INFO - process_video:173 - 视频项目处理完成: data\output\875fbd66-6e4c-4b7c-bab8-276181dc8284\Test_test_input.mp4.mp4
2025-07-21 13:40:40,169 - src.services.video_service - INFO - _validate_ffmpeg:33 - FFmpeg validation successful
2025-07-21 13:40:40,169 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 13:40:40,170 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 13:40:40,170 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 13:40:40,170 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 13:40:40,170 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 13:40:40,170 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 13:40:40,170 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 13:40:40,170 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 13:40:40,182 - src.workflow.engine - INFO - process_video:74 - 开始处理视频项目: 基础示例项目
2025-07-21 13:40:40,183 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 13:40:40,183 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 13:40:40,183 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 13:40:40,184 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 13:40:40,184 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 13:40:40,184 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 13:40:40,184 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 13:40:40,185 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 13:40:40,186 - src.services.file_service - INFO - create_project_workspace:72 - Created workspace for project d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3
2025-07-21 13:40:40,186 - src.workflow.engine - INFO - process_video:98 - 阶段1: 开始视频分析
2025-07-21 13:40:40,186 - src.services.ai_service - INFO - analyze_video:48 - 开始分析视频: data\input\example_input.mp4
2025-07-21 13:40:40,186 - src.services.ai_service - INFO - analyze_video:87 - 视频分析完成
2025-07-21 13:40:40,188 - src.workflow.engine - INFO - process_video:114 - 阶段2: 开始视频拆条
2025-07-21 13:40:40,189 - src.services.video_service - INFO - split_video:90 - 开始拆分视频: data\input\example_input.mp4 -> 3 个片段
2025-07-21 13:40:40,189 - src.services.video_service - INFO - get_video_metadata:49 - 获取视频元数据: data\input\example_input.mp4
2025-07-21 13:40:40,189 - src.services.video_service - INFO - get_video_metadata:62 - 视频元数据获取成功: 1920x1080, 30.0s
2025-07-21 13:40:40,189 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 1: 00:00:00.000 -> 8.5s
2025-07-21 13:40:40,190 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\segments\segment_001.mp4
2025-07-21 13:40:40,190 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 2: 00:00:08.500 -> 7.7s
2025-07-21 13:40:40,191 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\segments\segment_002.mp4
2025-07-21 13:40:40,191 - src.services.video_service - INFO - _extract_segment:162 - 提取视频片段 3: 00:00:16.200 -> 8.8s
2025-07-21 13:40:40,191 - src.services.video_service - INFO - _extract_segment:202 - 视频片段提取完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\segments\segment_003.mp4
2025-07-21 13:40:40,191 - src.services.video_service - INFO - split_video:132 - 视频拆分完成: 3 个片段
2025-07-21 13:40:40,192 - src.workflow.engine - INFO - process_video:130 - 阶段3: 开始旁白优化和语音合成
2025-07-21 13:40:40,192 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\segments\segment_001.mp4
2025-07-21 13:40:40,192 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 13:40:40,192 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\segments\segment_002.mp4
2025-07-21 13:40:40,193 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 13:40:40,193 - src.services.ai_service - INFO - optimize_narration:115 - 开始优化分镜旁白: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\segments\segment_003.mp4
2025-07-21 13:40:40,193 - src.services.ai_service - INFO - optimize_narration:134 - 旁白优化完成
2025-07-21 13:40:40,193 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,194 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_1_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,195 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_1_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,195 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,195 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_2_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,195 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_2_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,196 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影。... -> data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,196 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_3_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,197 - src.services.tts_service - INFO - synthesize_speech:58 - 开始合成语音: 大家好，这里是翔宇电影，今天我们来聊聊这... -> data\temp\audio\scene_3_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,199 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,199 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,199 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,200 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,200 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,200 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,201 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_2_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,201 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v2_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,201 - src.services.tts_service - INFO - synthesize_speech:79 - 语音合成完成: data\temp\audio\scene_3_v3_780716873dd0423a8568d82aeb17aa7c.mp3
2025-07-21 13:40:40,201 - src.services.tts_service - INFO - select_best_audio:253 - Scene 1: Selected audio with duration 1.00s (target: 8.50s, diff: 7.50s, score: 0.000)
2025-07-21 13:40:40,202 - src.services.tts_service - INFO - select_best_audio:253 - Scene 2: Selected audio with duration 1.00s (target: 7.70s, diff: 6.70s, score: 0.000)
2025-07-21 13:40:40,202 - src.services.tts_service - INFO - select_best_audio:253 - Scene 3: Selected audio with duration 1.00s (target: 8.80s, diff: 7.80s, score: 0.000)
2025-07-21 13:40:40,202 - src.workflow.engine - INFO - process_video:143 - 阶段4: 开始视频处理和音画同步
2025-07-21 13:40:40,202 - src.services.video_service - INFO - process_all_segments:438 - 开始处理 3 个视频片段
2025-07-21 13:40:40,203 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 1: 速度比例 8.500
2025-07-21 13:40:40,203 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\segments\segment_001.mp4 -> 8.5x
2025-07-21 13:40:40,204 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\speed_adjusted_1.mp4
2025-07-21 13:40:40,204 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\speed_adjusted_1.mp4 + data\temp\audio\scene_1_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\final_segment_1.mp4
2025-07-21 13:40:40,211 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\final_segment_1.mp4
2025-07-21 13:40:40,211 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\final_segment_1.mp4
2025-07-21 13:40:40,212 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 2: 速度比例 7.700
2025-07-21 13:40:40,212 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\segments\segment_002.mp4 -> 7.7x
2025-07-21 13:40:40,213 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\speed_adjusted_2.mp4
2025-07-21 13:40:40,213 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\speed_adjusted_2.mp4 + data\temp\audio\scene_2_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\final_segment_2.mp4
2025-07-21 13:40:40,214 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\final_segment_2.mp4
2025-07-21 13:40:40,215 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\final_segment_2.mp4
2025-07-21 13:40:40,215 - src.services.video_service - INFO - process_video_segment:388 - 处理视频片段 3: 速度比例 8.800
2025-07-21 13:40:40,215 - src.services.video_service - INFO - adjust_video_speed:228 - 调整视频速度: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\segments\segment_003.mp4 -> 8.8x
2025-07-21 13:40:40,216 - src.services.video_service - INFO - adjust_video_speed:241 - 视频调速完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\speed_adjusted_3.mp4
2025-07-21 13:40:40,216 - src.services.video_service - INFO - merge_audio_video:267 - 合并音视频: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\speed_adjusted_3.mp4 + data\temp\audio\scene_3_v1_780716873dd0423a8568d82aeb17aa7c.mp3 -> data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\final_segment_3.mp4
2025-07-21 13:40:40,219 - src.services.video_service - INFO - merge_audio_video:283 - 音视频合并完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\final_segment_3.mp4
2025-07-21 13:40:40,219 - src.services.video_service - INFO - process_video_segment:415 - 视频片段处理完成: data\temp\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\processing\final_segment_3.mp4
2025-07-21 13:40:40,220 - src.services.video_service - INFO - process_all_segments:475 - 所有视频片段处理完成: 3 个
2025-07-21 13:40:40,221 - src.workflow.engine - INFO - process_video:159 - 阶段5: 开始最终视频合成
2025-07-21 13:40:40,221 - src.services.video_service - INFO - create_final_video:501 - 创建最终视频: 3 个片段 -> data\output\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\基础示例项目.mp4
2025-07-21 13:40:40,221 - src.services.video_service - INFO - concatenate_videos:310 - 拼接视频: 3 个文件 -> data\output\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\基础示例项目.mp4
2025-07-21 13:40:40,223 - src.services.video_service - INFO - concatenate_videos:327 - 视频拼接完成: data\output\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\基础示例项目.mp4
2025-07-21 13:40:40,224 - src.services.video_service - INFO - create_final_video:509 - 最终视频创建完成: data\output\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\基础示例项目.mp4
2025-07-21 13:40:40,224 - src.workflow.engine - INFO - process_video:173 - 视频项目处理完成: data\output\d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3\基础示例项目.mp4
2025-07-21 13:40:40,261 - src.services.video_service - INFO - _validate_ffmpeg:33 - FFmpeg validation successful
2025-07-21 13:40:40,262 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 13:40:40,263 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 13:40:40,263 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 13:40:40,263 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 13:40:40,263 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 13:40:40,264 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 13:40:40,264 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 13:40:40,265 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 13:40:40,265 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 13:40:40,265 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 13:40:40,265 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 13:40:40,266 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 13:40:40,266 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 13:40:40,266 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 13:40:40,266 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 13:40:40,266 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 13:40:40,310 - src.services.video_service - INFO - _validate_ffmpeg:33 - FFmpeg validation successful
2025-07-21 13:40:40,311 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data
2025-07-21 13:40:40,311 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\input
2025-07-21 13:40:40,312 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\output
2025-07-21 13:40:40,313 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp
2025-07-21 13:40:40,313 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\cache
2025-07-21 13:40:40,313 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\segments
2025-07-21 13:40:40,313 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\audio
2025-07-21 13:40:40,314 - src.services.file_service - DEBUG - _init_directories:43 - Ensured directory exists: data\temp\processing
2025-07-21 13:40:40,315 - src.services.file_service - INFO - cleanup_project:376 - Cleaned project d5df6d4d-3ac5-4fa7-958a-85aaf3c2ecb3 temp files
