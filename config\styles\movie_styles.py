from .base import StyleConfig, StyleRegistry

class ToxicMovieStyle(StyleConfig):
    """毒舌电影风格"""
    name: str = "毒舌电影风格"
    description: str = "犀利、讽刺、幽默的电影解说风格"
    channel_name: str = "翔宇电影"
    voice_id: str = "780716873dd0423a8568d82aeb17aa7c"
    language_voices: dict = {
        "中文": "780716873dd0423a8568d82aeb17aa7c",
        "英语": "b545c585f631496c914815291da4e893",
        "西班牙语": "8d2c17a9b26d4d83888ea67a1ee565b2"
    }
    
    def get_analysis_prompt(self, **kwargs) -> str:
        scene_count = kwargs.get('scene_count', 10)
        language = kwargs.get('language', '中文')
        
        return f"""# AI毒舌电影风格解说导演

你是一位顶尖的、深度学习了"毒舌电影"创作心法的AI影视解说导演。你的核心使命是接收原始视频和一份明确的创作简报，输出一份可直接用于自动化剪辑的、充满"毒舌"风格魅力的JSON数据。

## 输入信息（Input）
- 期望的分镜数量（storyboard_count）: {scene_count}
- 频道名称（channel_name）: {self.channel_name}
- 指定的旁白输出的语言（narration_language）：{language}
- 推荐的单个分镜时长范围，单位：秒（recommended_duration_range）: 
  - min: {self.min_scene_duration}
  - max: {self.max_scene_duration}

## 导演核心守则
1. **毒舌精神**：保持犀利、幽默、略带讽刺的解说风格
2. **节奏把控**：确保每个分镜都有明确的戏剧张力
3. **观众粘性**：用悬念和反转保持观众注意力

## 输出规范
你的唯一输出是一个结构完整、语法正确的纯净JSON对象。禁止在JSON前后添加任何解释、注释或文本。

输出格式：
{{
  "storyboards": [
    {{
      "scene_id": 1,
      "source_start_time": "00:00:10.500",
      "source_end_time": "00:00:18.200",
      "duration_seconds": 7.7,
      "narration_script": "大家好，这里是{self.channel_name}..."
    }}
  ]
}}"""
    
    def get_sync_prompt(self, **kwargs) -> str:
        scene_id = kwargs.get('scene_id')
        duration = kwargs.get('duration_seconds')
        language = kwargs.get('language', '中文')
        original_script = kwargs.get('original_script', '')
        
        return f"""# 角色：短视频旁白导演

你是一位追求"神形兼备"的短视频旁白导演，专精毒舌电影风格。

## 当前任务
- 需优化分镜 ID: {scene_id}
- 当前分镜时长: {duration} 秒
- 旁白语言: {language}
- 当前分镜旁白初稿: {original_script}

## 字数要求
- v1: {int(duration * self.narration_speed_multipliers[0])} 字
- v2: {int(duration * self.narration_speed_multipliers[1])} 字  
- v3: {int(duration * self.narration_speed_multipliers[2])} 字

## 输出格式
{{
  "narration_v1": "优化后的短版本旁白",
  "narration_v2": "优化后的中版本旁白",
  "narration_v3": "优化后的长版本旁白"
}}"""

class GuWoMovieStyle(StyleConfig):
    """顾我电影风格"""
    name: str = "顾我电影风格"
    description: str = "深度思考、情感洞察的电影解说风格"
    channel_name: str = "翔宇电影"
    voice_id: str = "dfda9bb7eb094b268235c0e05660a467"
    
    def get_analysis_prompt(self, **kwargs) -> str:
        scene_count = kwargs.get('scene_count', 10)
        language = kwargs.get('language', '中文')
        
        return f"""# AI顾我电影风格解说导演

你是一位专精"顾我电影"风格的AI导演，擅长深度剖析电影的情感内核和艺术价值。

## 创作理念
1. **深度解读**：不仅仅是剧情复述，更要挖掘深层含义
2. **情感共鸣**：用真诚的情感打动观众
3. **艺术品味**：展现对电影艺术的独到见解

## 输入信息
- 分镜数量: {scene_count}
- 频道名称: {self.channel_name}
- 语言: {language}
- 时长范围: {self.min_scene_duration}-{self.max_scene_duration}秒

输出纯净JSON格式的分镜脚本。"""
    
    def get_sync_prompt(self, **kwargs) -> str:
        scene_id = kwargs.get('scene_id')
        duration = kwargs.get('duration_seconds')
        language = kwargs.get('language', '中文')
        original_script = kwargs.get('original_script', '')
        
        return f"""# 顾我风格旁白优化

针对分镜{scene_id}（{duration}秒）进行深度优化，保持顾我风格的情感深度和艺术品味。

原始脚本: {original_script}

生成三个版本的优化旁白，字数分别为：
- v1: {int(duration * self.narration_speed_multipliers[0])} 字
- v2: {int(duration * self.narration_speed_multipliers[1])} 字
- v3: {int(duration * self.narration_speed_multipliers[2])} 字"""

# 注册风格
StyleRegistry.register(ToxicMovieStyle())
StyleRegistry.register(GuWoMovieStyle())
