from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Dict, List, Optional
import os

class Settings(BaseSettings):
    # 项目基础配置
    PROJECT_NAME: str = "Video Director Pro"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # 文件路径配置
    DATA_DIR: str = Field(default="./data", description="数据根目录")
    INPUT_DIR: str = Field(default="./data/input", description="输入目录")
    OUTPUT_DIR: str = Field(default="./data/output", description="输出目录")
    TEMP_DIR: str = Field(default="./data/temp", description="临时目录")
    CACHE_DIR: str = Field(default="./data/cache", description="缓存目录")
    
    # API配置
    GEMINI_API_KEY: str = Field(..., env="GEMINI_API_KEY")
    GEMINI_PROJECT_ID: str = Field(..., env="GEMINI_PROJECT_ID")
    GEMINI_MODEL: str = Field(default="gemini-2.5-pro", env="GEMINI_MODEL")
    
    FISH_AUDIO_API_KEY: str = Field(..., env="FISH_AUDIO_API_KEY")
    FISH_AUDIO_MODEL: str = Field(default="speech-1.6", env="FISH_AUDIO_MODEL")
    
    # FFmpeg配置
    FFMPEG_PATH: str = Field(default="ffmpeg", env="FFMPEG_PATH")
    FFPROBE_PATH: str = Field(default="ffprobe", env="FFPROBE_PATH")
    
    # 处理配置
    MAX_CONCURRENT_TASKS: int = Field(default=3, description="最大并发任务数")
    AUDIO_BITRATE: int = Field(default=128, description="音频比特率")
    VIDEO_CRF: int = Field(default=23, description="视频质量参数")
    
    # 重试配置
    MAX_RETRIES: int = Field(default=3, description="最大重试次数")
    RETRY_DELAY: float = Field(default=1.0, description="重试延迟(秒)")

    # API服务配置
    API_HOST: str = Field(default="0.0.0.0", env="API_HOST")
    API_PORT: int = Field(default=8000, env="API_PORT")
    API_WORKERS: int = Field(default=1, env="API_WORKERS")

    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field(default="./data/logs/video_director.log", env="LOG_FILE")
    ENABLE_JSON_LOGS: bool = Field(default=False, env="ENABLE_JSON_LOGS")

    class Config:
        env_file = ".env"
        case_sensitive = True

# 全局设置实例
settings = Settings()
