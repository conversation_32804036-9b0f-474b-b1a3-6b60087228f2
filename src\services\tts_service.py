import asyncio
import aiohttp
import aiofiles
from typing import List, Dict, Any, Optional
from pathlib import Path
import hashlib
import json

from ..models.audio import AudioFile, AudioMetadata, AudioCandidate, AudioSelection
from ..models.storyboard import NarrationVersion
from ..utils.exceptions import TTSServiceError, AudioProcessingError
from ..utils.logger import get_logger
from ..utils.retry import api_retry
from config.settings import settings

logger = get_logger(__name__)

class FishAudioService:
    """Fish Audio TTS服务封装"""

    def __init__(self):
        self.api_key = settings.FISH_AUDIO_API_KEY
        self.model = settings.FISH_AUDIO_MODEL
        self.base_url = "https://api.fish.audio/v1/tts"
        self.max_concurrent = settings.MAX_CONCURRENT_TASKS

    @api_retry
    async def synthesize_speech(
        self,
        text: str,
        voice_id: str,
        language: str = "中文",
        temperature: float = 0.7,
        top_p: float = 0.7,
        output_path: Optional[str] = None
    ) -> AudioFile:
        """
        合成单个语音

        Args:
            text: 文本内容
            voice_id: 音色ID
            language: 语言
            temperature: 温度参数
            top_p: Top-p参数
            output_path: 输出路径

        Returns:
            音频文件对象
        """
        try:
            # 生成输出路径
            if not output_path:
                text_hash = hashlib.md5(text.encode()).hexdigest()[:8]
                output_path = Path(settings.TEMP_DIR) / "audio" / f"tts_{text_hash}_{voice_id}.mp3"
                output_path.parent.mkdir(parents=True, exist_ok=True)

            logger.info(f"开始合成语音: {text[:20]}... -> {output_path}")

            # 模拟语音合成 - 在实际实现中调用Fish Audio API
            # 这里创建一个模拟的音频文件
            mock_audio_content = b"mock_audio_data_" + text.encode()

            async with aiofiles.open(output_path, 'wb') as f:
                await f.write(mock_audio_content)

            # 获取音频元数据
            metadata = await self._get_audio_metadata(str(output_path))

            # 创建AudioFile对象
            audio_file = AudioFile(
                file_path=str(output_path),
                narration_text=text,
                voice_id=voice_id,
                language=language,
                metadata=metadata
            )

            logger.info(f"语音合成完成: {output_path}")
            return audio_file

        except Exception as e:
            logger.error(f"Speech synthesis failed: {str(e)}")
            raise TTSServiceError(f"Failed to synthesize speech: {str(e)}")

    async def synthesize_multiple_versions(
        self,
        narration_versions: List[NarrationVersion],
        voice_id: str,
        language: str,
        scene_id: int
    ) -> List[AudioFile]:
        """
        并发合成多个版本的语音

        Args:
            narration_versions: 旁白版本列表
            voice_id: 音色ID
            language: 语言
            scene_id: 分镜ID

        Returns:
            音频文件列表
        """
        try:
            # 创建并发任务
            tasks = []
            for version in narration_versions:
                output_path = Path(settings.TEMP_DIR) / "audio" / f"scene_{scene_id}_{version.version}_{voice_id}.mp3"

                task = self.synthesize_speech(
                    text=version.text,
                    voice_id=voice_id,
                    language=language,
                    output_path=str(output_path)
                )
                tasks.append(task)

            # 限制并发数量
            semaphore = asyncio.Semaphore(self.max_concurrent)

            async def bounded_synthesis(task):
                async with semaphore:
                    return await task

            # 执行并发合成
            audio_files = await asyncio.gather(
                *[bounded_synthesis(task) for task in tasks],
                return_exceptions=True
            )

            # 处理结果
            successful_files = []
            for i, result in enumerate(audio_files):
                if isinstance(result, Exception):
                    logger.error(f"Failed to synthesize version {narration_versions[i].version}: {str(result)}")
                else:
                    successful_files.append(result)

            if not successful_files:
                raise TTSServiceError("All speech synthesis attempts failed")

            return successful_files

        except Exception as e:
            logger.error(f"Multiple speech synthesis failed: {str(e)}")
            raise TTSServiceError(f"Failed to synthesize multiple versions: {str(e)}")

    async def _get_audio_metadata(self, audio_path: str) -> AudioMetadata:
        """
        获取音频文件元数据

        Args:
            audio_path: 音频文件路径

        Returns:
            音频元数据
        """
        try:
            # 获取文件大小
            file_size = Path(audio_path).stat().st_size

            # 模拟音频元数据 - 在实际实现中使用ffprobe
            # 根据文件大小估算时长
            estimated_duration = max(1.0, file_size / 1000.0)  # 简单估算

            # 创建元数据对象
            metadata = AudioMetadata(
                duration=estimated_duration,
                sample_rate=44100,
                channels=1,
                bitrate=settings.AUDIO_BITRATE * 1000,
                format="mp3",
                file_size=file_size
            )

            return metadata

        except Exception as e:
            logger.error(f"Failed to get audio metadata: {str(e)}")
            # 返回默认元数据
            return AudioMetadata(
                duration=5.0,  # 默认5秒
                sample_rate=44100,
                channels=1,
                bitrate=settings.AUDIO_BITRATE * 1000,
                format="mp3",
                file_size=Path(audio_path).stat().st_size if Path(audio_path).exists() else 0
            )

class AudioSelectionService:
    """音频选择服务"""

    def __init__(self):
        pass

    def select_best_audio(
        self,
        audio_files: List[AudioFile],
        target_duration: float,
        scene_id: int
    ) -> AudioSelection:
        """
        选择最佳音频文件

        Args:
            audio_files: 候选音频文件列表
            target_duration: 目标时长
            scene_id: 分镜ID

        Returns:
            音频选择结果
        """
        try:
            if not audio_files:
                raise AudioProcessingError("No audio files to select from")

            # 创建候选项
            candidates = []
            for audio_file in audio_files:
                duration_diff = abs(audio_file.metadata.duration - target_duration)

                # 计算匹配分数 (时长差异越小分数越高)
                max_diff = max(target_duration * 0.5, 2.0)  # 最大允许差异
                match_score = max(0, 1 - (duration_diff / max_diff))

                candidate = AudioCandidate(
                    audio_file=audio_file,
                    target_duration=target_duration,
                    duration_diff=duration_diff,
                    match_score=match_score
                )
                candidates.append(candidate)

            # 按匹配分数排序
            candidates.sort(key=lambda x: x.match_score, reverse=True)

            # 选择最佳候选项
            selected = candidates[0]

            # 生成选择原因
            reason = f"Selected audio with duration {selected.audio_file.metadata.duration:.2f}s " \
                    f"(target: {target_duration:.2f}s, diff: {selected.duration_diff:.2f}s, " \
                    f"score: {selected.match_score:.3f})"

            selection = AudioSelection(
                scene_id=scene_id,
                candidates=candidates,
                selected=selected,
                selection_reason=reason
            )

            logger.info(f"Scene {scene_id}: {reason}")
            return selection

        except Exception as e:
            logger.error(f"Audio selection failed for scene {scene_id}: {str(e)}")
            raise AudioProcessingError(f"Failed to select best audio: {str(e)}")

class TTSWorkflowService:
    """TTS工作流服务"""

    def __init__(self):
        self.fish_audio = FishAudioService()
        self.audio_selector = AudioSelectionService()
        self.max_concurrent = settings.MAX_CONCURRENT_TASKS

    async def process_scene_audio(
        self,
        narration_versions: List[NarrationVersion],
        voice_id: str,
        language: str,
        scene_id: int,
        target_duration: float
    ) -> AudioSelection:
        """
        处理单个分镜的音频生成和选择

        Args:
            narration_versions: 旁白版本列表
            voice_id: 音色ID
            language: 语言
            scene_id: 分镜ID
            target_duration: 目标时长

        Returns:
            音频选择结果
        """
        try:
            # 并发生成多版本音频
            audio_files = await self.fish_audio.synthesize_multiple_versions(
                narration_versions=narration_versions,
                voice_id=voice_id,
                language=language,
                scene_id=scene_id
            )

            # 选择最佳音频
            selection = self.audio_selector.select_best_audio(
                audio_files=audio_files,
                target_duration=target_duration,
                scene_id=scene_id
            )

            return selection

        except Exception as e:
            logger.error(f"Scene audio processing failed for scene {scene_id}: {str(e)}")
            raise TTSServiceError(f"Failed to process scene audio: {str(e)}")

    async def process_all_scenes_audio(
        self,
        scenes_data: List[Dict[str, Any]],
        voice_id: str,
        language: str
    ) -> List[AudioSelection]:
        """
        处理所有分镜的音频生成

        Args:
            scenes_data: 分镜数据列表，每个包含narration_versions, scene_id, target_duration
            voice_id: 音色ID
            language: 语言

        Returns:
            音频选择结果列表
        """
        try:
            # 创建并发任务
            tasks = []
            for scene_data in scenes_data:
                task = self.process_scene_audio(
                    narration_versions=scene_data["narration_versions"],
                    voice_id=voice_id,
                    language=language,
                    scene_id=scene_data["scene_id"],
                    target_duration=scene_data["target_duration"]
                )
                tasks.append(task)

            # 限制并发数量
            semaphore = asyncio.Semaphore(self.max_concurrent)

            async def bounded_processing(task):
                async with semaphore:
                    return await task

            # 执行并发处理
            results = await asyncio.gather(
                *[bounded_processing(task) for task in tasks],
                return_exceptions=True
            )

            # 处理结果
            selections = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Failed to process scene {scenes_data[i]['scene_id']}: {str(result)}")
                    raise TTSServiceError(f"Scene processing failed: {str(result)}")
                else:
                    selections.append(result)

            return selections

        except Exception as e:
            logger.error(f"All scenes audio processing failed: {str(e)}")
            raise TTSServiceError(f"Failed to process all scenes audio: {str(e)}")
