# Video Director Pro

🎬 全自动视频剪辑系统 - Python异步实现版本

## 功能特性

- 🤖 **AI驱动**: 使用Google Gemini 2.5 Pro进行视频内容分析
- 🎵 **智能配音**: Fish Audio高质量语音合成，支持多语言
- ⚡ **异步处理**: 高性能并发处理，支持大规模视频处理
- 🎨 **多种风格**: 多种预设剪辑风格，满足不同需求
- 🔧 **本地处理**: 使用本地FFmpeg，无需云服务依赖
- 📊 **实时监控**: 完整的进度跟踪和错误处理

## 快速开始

### 1. 环境要求

- Python 3.8+
- FFmpeg 4.0+
- 8GB+ RAM (推荐16GB)

### 2. 安装

```bash
# 克隆项目
git clone https://github.com/your-repo/video-director-pro.git
cd video-director-pro

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件，填入API密钥
# 必需的API密钥：
# - GEMINI_API_KEY: Google Gemini API密钥
# - FISH_AUDIO_API_KEY: Fish Audio API密钥
```

### 4. 测试安装

```bash
# 测试基础功能
python cli/main.py test

# 查看系统状态
python cli/main.py status

# 查看可用风格
python cli/main.py styles
```

## 项目结构

```
video_director_pro/
├── src/                    # 源代码
│   ├── models/            # 数据模型
│   ├── services/          # 服务层
│   ├── workflow/          # 工作流引擎
│   ├── utils/             # 工具类
│   └── api/               # API接口
├── config/                # 配置文件
├── cli/                   # 命令行界面
├── tests/                 # 测试代码
├── data/                  # 数据目录
└── scripts/               # 脚本文件
```

## 开发状态

当前版本：v1.0.0-alpha

- ✅ 项目架构和基础结构
- ✅ 核心数据模型
- ✅ 配置管理系统
- ✅ 错误处理和日志系统
- 🚧 AI服务接口 (开发中)
- 🚧 语音合成服务 (开发中)
- 🚧 视频处理服务 (开发中)
- 🚧 工作流引擎 (开发中)

## 许可证

MIT License
