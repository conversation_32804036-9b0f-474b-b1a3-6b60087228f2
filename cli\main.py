#!/usr/bin/env python3
"""
Video Director Pro - 命令行界面
"""

import sys
from pathlib import Path
import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

console = Console()

@click.group()
@click.version_option(version="1.0.0")
def cli():
    """🎬 Video Director Pro - 全自动视频剪辑系统"""
    pass

@cli.command()
def styles():
    """列出可用的剪辑风格"""
    try:
        from config import config_manager
        available_styles = config_manager.list_available_styles()
        
        table = Table(title="🎨 可用剪辑风格")
        table.add_column("风格名称", style="cyan")
        table.add_column("描述", style="green")
        
        for style_name in available_styles:
            style_config = config_manager.get_style(style_name)
            table.add_row(style_name, style_config.description)
        
        console.print(table)
    except Exception as e:
        console.print(f"❌ 错误: {str(e)}", style="red")

@cli.command()
def status():
    """显示系统状态"""
    try:
        from config import config_manager
        
        # 检查配置
        try:
            config_manager.validate_config()
            config_status = "✅ 正常"
        except Exception as e:
            config_status = f"❌ 错误: {str(e)}"
        
        # 检查FFmpeg
        import shutil
        ffmpeg_status = "✅ 已安装" if shutil.which("ffmpeg") else "❌ 未安装"
        
        # 显示状态表
        table = Table(title="🔍 系统状态")
        table.add_column("组件", style="cyan")
        table.add_column("状态", style="green")
        
        table.add_row("配置文件", config_status)
        table.add_row("FFmpeg", ffmpeg_status)
        table.add_row("可用风格", str(len(config_manager.list_available_styles())))
        
        console.print(table)
    except Exception as e:
        console.print(f"❌ 错误: {str(e)}", style="red")

@cli.command()
def test():
    """测试基础功能"""
    console.print("🧪 测试基础功能...", style="yellow")

    try:
        # 测试配置加载
        console.print("1. 测试配置加载...", style="cyan")
        from config import config_manager
        console.print(f"   ✅ 配置加载成功，可用风格: {len(config_manager.list_available_styles())}")

        # 测试数据模型
        console.print("2. 测试数据模型...", style="cyan")
        from src.models import TimeRange, Storyboard
        time_range = TimeRange(start_time="00:00:10.500", end_time="00:00:18.200", duration_seconds=7.7)
        console.print(f"   ✅ 数据模型测试成功，时长: {time_range.duration_seconds}秒")

        # 测试日志系统
        console.print("3. 测试日志系统...", style="cyan")
        from src.utils import get_logger
        logger = get_logger("test")
        logger.info("测试日志消息")
        console.print("   ✅ 日志系统测试成功")

        console.print()
        console.print(Panel.fit(
            "✅ 所有基础功能测试通过！\n系统已准备就绪。",
            title="🎉 测试结果",
            border_style="green"
        ))

    except Exception as e:
        console.print()
        console.print(Panel.fit(
            f"❌ 测试失败: {str(e)}",
            title="错误",
            border_style="red"
        ))
        sys.exit(1)

@cli.command()
@click.argument('video_path', required=False, default="test_video.mp4")
@click.option('--style', '-s', default='毒舌电影风格', help='剪辑风格')
@click.option('--scenes', '-n', default=3, help='分镜数量')
def test_ai(video_path, style, scenes):
    """测试AI服务"""
    import asyncio

    async def run_ai_test():
        console.print("🤖 测试AI服务...", style="yellow")

        try:
            from config import config_manager
            from src.services.ai_service import AIAnalysisService

            # 获取风格配置
            console.print(f"1. 获取风格配置: {style}", style="cyan")
            style_config = config_manager.get_style(style)
            console.print(f"   ✅ 风格配置获取成功: {style_config.description}")

            # 创建AI服务
            console.print("2. 创建AI分析服务...", style="cyan")
            ai_service = AIAnalysisService()
            console.print("   ✅ AI服务创建成功")

            # 测试视频分析
            console.print(f"3. 测试视频分析 (模拟): {video_path}", style="cyan")
            storyboard_collection = await ai_service.analyze_video_with_style(
                video_path=video_path,
                style_config=style_config,
                scene_count=scenes,
                language="中文"
            )
            console.print(f"   ✅ 视频分析成功，生成 {len(storyboard_collection.storyboards)} 个分镜")

            # 显示分镜信息
            for sb in storyboard_collection.storyboards:
                console.print(f"     分镜{sb.scene_id}: {sb.source_time_range.start_time}-{sb.source_time_range.end_time} ({sb.source_time_range.duration_seconds}s)")
                console.print(f"     旁白: {sb.narration_script[:50]}...")

            # 测试旁白优化
            console.print("4. 测试旁白优化...", style="cyan")
            first_storyboard = storyboard_collection.storyboards[0]
            narration_versions = await ai_service.optimize_scene_narration(
                storyboard=first_storyboard,
                video_segment_path="test_segment.mp4",
                style_config=style_config,
                language="中文"
            )
            console.print(f"   ✅ 旁白优化成功，生成 {len(narration_versions)} 个版本")

            for version in narration_versions:
                console.print(f"     {version.version}: {version.text} ({version.word_count}字)")

            console.print()
            console.print(Panel.fit(
                "✅ AI服务测试通过！\n模拟的视频分析和旁白优化功能正常工作。",
                title="🎉 AI测试结果",
                border_style="green"
            ))

        except Exception as e:
            console.print()
            console.print(Panel.fit(
                f"❌ AI服务测试失败: {str(e)}",
                title="错误",
                border_style="red"
            ))
            sys.exit(1)

    asyncio.run(run_ai_test())

if __name__ == '__main__':
    cli()
