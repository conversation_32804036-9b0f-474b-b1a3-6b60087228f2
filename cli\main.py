#!/usr/bin/env python3
"""
Video Director Pro - 命令行界面
"""

import sys
from pathlib import Path
import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

console = Console()

@click.group()
@click.version_option(version="1.0.0")
def cli():
    """🎬 Video Director Pro - 全自动视频剪辑系统"""
    pass

@cli.command()
def styles():
    """列出可用的剪辑风格"""
    try:
        from config import config_manager
        available_styles = config_manager.list_available_styles()
        
        table = Table(title="🎨 可用剪辑风格")
        table.add_column("风格名称", style="cyan")
        table.add_column("描述", style="green")
        
        for style_name in available_styles:
            style_config = config_manager.get_style(style_name)
            table.add_row(style_name, style_config.description)
        
        console.print(table)
    except Exception as e:
        console.print(f"❌ 错误: {str(e)}", style="red")

@cli.command()
def status():
    """显示系统状态"""
    try:
        from config import config_manager
        
        # 检查配置
        try:
            config_manager.validate_config()
            config_status = "✅ 正常"
        except Exception as e:
            config_status = f"❌ 错误: {str(e)}"
        
        # 检查FFmpeg
        import shutil
        ffmpeg_status = "✅ 已安装" if shutil.which("ffmpeg") else "❌ 未安装"
        
        # 显示状态表
        table = Table(title="🔍 系统状态")
        table.add_column("组件", style="cyan")
        table.add_column("状态", style="green")
        
        table.add_row("配置文件", config_status)
        table.add_row("FFmpeg", ffmpeg_status)
        table.add_row("可用风格", str(len(config_manager.list_available_styles())))
        
        console.print(table)
    except Exception as e:
        console.print(f"❌ 错误: {str(e)}", style="red")

@cli.command()
def test():
    """测试基础功能"""
    console.print("🧪 测试基础功能...", style="yellow")

    try:
        # 测试配置加载
        console.print("1. 测试配置加载...", style="cyan")
        from config import config_manager
        console.print(f"   ✅ 配置加载成功，可用风格: {len(config_manager.list_available_styles())}")

        # 测试数据模型
        console.print("2. 测试数据模型...", style="cyan")
        from src.models import TimeRange, Storyboard
        time_range = TimeRange(start_time="00:00:10.500", end_time="00:00:18.200", duration_seconds=7.7)
        console.print(f"   ✅ 数据模型测试成功，时长: {time_range.duration_seconds}秒")

        # 测试日志系统
        console.print("3. 测试日志系统...", style="cyan")
        from src.utils import get_logger
        logger = get_logger("test")
        logger.info("测试日志消息")
        console.print("   ✅ 日志系统测试成功")

        console.print()
        console.print(Panel.fit(
            "✅ 所有基础功能测试通过！\n系统已准备就绪。",
            title="🎉 测试结果",
            border_style="green"
        ))

    except Exception as e:
        console.print()
        console.print(Panel.fit(
            f"❌ 测试失败: {str(e)}",
            title="错误",
            border_style="red"
        ))
        sys.exit(1)

@cli.command()
@click.argument('video_path', required=False, default="test_video.mp4")
@click.option('--style', '-s', default='毒舌电影风格', help='剪辑风格')
@click.option('--scenes', '-n', default=3, help='分镜数量')
def test_ai(video_path, style, scenes):
    """测试AI服务"""
    import asyncio

    async def run_ai_test():
        console.print("🤖 测试AI服务...", style="yellow")

        try:
            from config import config_manager
            from src.services.ai_service import AIAnalysisService

            # 获取风格配置
            console.print(f"1. 获取风格配置: {style}", style="cyan")
            style_config = config_manager.get_style(style)
            console.print(f"   ✅ 风格配置获取成功: {style_config.description}")

            # 创建AI服务
            console.print("2. 创建AI分析服务...", style="cyan")
            ai_service = AIAnalysisService()
            console.print("   ✅ AI服务创建成功")

            # 测试视频分析
            console.print(f"3. 测试视频分析 (模拟): {video_path}", style="cyan")
            storyboard_collection = await ai_service.analyze_video_with_style(
                video_path=video_path,
                style_config=style_config,
                scene_count=scenes,
                language="中文"
            )
            console.print(f"   ✅ 视频分析成功，生成 {len(storyboard_collection.storyboards)} 个分镜")

            # 显示分镜信息
            for sb in storyboard_collection.storyboards:
                console.print(f"     分镜{sb.scene_id}: {sb.source_time_range.start_time}-{sb.source_time_range.end_time} ({sb.source_time_range.duration_seconds}s)")
                console.print(f"     旁白: {sb.narration_script[:50]}...")

            # 测试旁白优化
            console.print("4. 测试旁白优化...", style="cyan")
            first_storyboard = storyboard_collection.storyboards[0]
            narration_versions = await ai_service.optimize_scene_narration(
                storyboard=first_storyboard,
                video_segment_path="test_segment.mp4",
                style_config=style_config,
                language="中文"
            )
            console.print(f"   ✅ 旁白优化成功，生成 {len(narration_versions)} 个版本")

            for version in narration_versions:
                console.print(f"     {version.version}: {version.text} ({version.word_count}字)")

            console.print()
            console.print(Panel.fit(
                "✅ AI服务测试通过！\n模拟的视频分析和旁白优化功能正常工作。",
                title="🎉 AI测试结果",
                border_style="green"
            ))

        except Exception as e:
            console.print()
            console.print(Panel.fit(
                f"❌ AI服务测试失败: {str(e)}",
                title="错误",
                border_style="red"
            ))
            sys.exit(1)

    asyncio.run(run_ai_test())

@cli.command()
def test_tts():
    """测试语音合成服务"""
    import asyncio

    async def run_tts_test():
        console.print("🎵 测试语音合成服务...", style="yellow")

        try:
            from src.services.tts_service import TTSWorkflowService
            from src.models.storyboard import NarrationVersion

            # 创建TTS服务
            console.print("1. 创建TTS工作流服务...", style="cyan")
            tts_service = TTSWorkflowService()
            console.print("   ✅ TTS服务创建成功")

            # 创建测试旁白版本
            console.print("2. 创建测试旁白版本...", style="cyan")
            narration_versions = [
                NarrationVersion(
                    version="v1",
                    text="大家好，这里是翔宇电影。",
                    word_count=12,
                    estimated_duration=3.0
                ),
                NarrationVersion(
                    version="v2",
                    text="大家好，这里是翔宇电影，今天我们来聊聊这部电影。",
                    word_count=24,
                    estimated_duration=5.3
                ),
                NarrationVersion(
                    version="v3",
                    text="大家好，这里是翔宇电影，今天我们来聊聊这部让人又爱又恨的经典电影作品。",
                    word_count=35,
                    estimated_duration=6.4
                )
            ]
            console.print(f"   ✅ 创建了 {len(narration_versions)} 个旁白版本")

            # 测试音频生成和选择
            console.print("3. 测试音频生成和选择...", style="cyan")
            audio_selection = await tts_service.process_scene_audio(
                narration_versions=narration_versions,
                voice_id="test_voice_id",
                language="中文",
                scene_id=1,
                target_duration=5.0
            )

            console.print(f"   ✅ 音频处理成功")
            console.print(f"     选择的版本: {audio_selection.selected.audio_file.narration_text}")
            console.print(f"     音频时长: {audio_selection.selected.audio_file.metadata.duration:.2f}s")
            console.print(f"     匹配分数: {audio_selection.selected.match_score:.3f}")
            console.print(f"     选择原因: {audio_selection.selection_reason}")

            console.print()
            console.print(Panel.fit(
                "✅ TTS服务测试通过！\n模拟的语音合成和音频选择功能正常工作。",
                title="🎉 TTS测试结果",
                border_style="green"
            ))

        except Exception as e:
            console.print()
            console.print(Panel.fit(
                f"❌ TTS服务测试失败: {str(e)}",
                title="错误",
                border_style="red"
            ))
            sys.exit(1)

    asyncio.run(run_tts_test())

@cli.command()
def test_video():
    """测试视频处理服务"""
    import asyncio

    async def run_video_test():
        console.print("🎬 测试视频处理服务...", style="yellow")

        try:
            from src.services.video_service import VideoWorkflowService
            from src.models.video import VideoSegment, VideoMetadata
            from src.models.audio import AudioFile, AudioMetadata
            from src.models.base import TimeRange

            # 创建视频服务
            console.print("1. 创建视频工作流服务...", style="cyan")
            video_service = VideoWorkflowService()
            console.print("   ✅ 视频服务创建成功")

            # 创建测试视频片段
            console.print("2. 创建测试视频片段...", style="cyan")

            # 创建测试文件
            from pathlib import Path
            test_segment_path = Path("./data/temp/test_segment.mp4")
            test_segment_path.parent.mkdir(parents=True, exist_ok=True)
            with open(test_segment_path, 'wb') as f:
                f.write(b"mock_video_segment_data")

            time_range = TimeRange(
                start_time="00:00:00.000",
                end_time="00:00:08.500",
                duration_seconds=8.5
            )

            video_metadata = VideoMetadata(
                width=1920,
                height=1080,
                fps=25.0,
                duration=8.5,
                codec="h264",
                bitrate=2000000,
                file_size=10 * 1024 * 1024
            )

            video_segment = VideoSegment(
                scene_id=1,
                source_video_path="test_input.mp4",
                segment_path=str(test_segment_path),
                time_range=time_range,
                metadata=video_metadata
            )
            console.print("   ✅ 测试视频片段创建成功")

            # 创建测试音频文件
            console.print("3. 创建测试音频文件...", style="cyan")

            # 创建测试音频文件
            test_audio_path = Path("./data/temp/test_audio.mp3")
            test_audio_path.parent.mkdir(parents=True, exist_ok=True)
            with open(test_audio_path, 'wb') as f:
                f.write(b"mock_audio_data")

            audio_metadata = AudioMetadata(
                duration=7.5,
                sample_rate=44100,
                channels=1,
                bitrate=128000,
                format="mp3",
                file_size=1024 * 1024
            )

            audio_file = AudioFile(
                file_path=str(test_audio_path),
                narration_text="测试旁白文本",
                voice_id="test_voice",
                language="中文",
                metadata=audio_metadata
            )
            console.print("   ✅ 测试音频文件创建成功")

            # 测试视频片段处理
            console.print("4. 测试视频片段处理...", style="cyan")
            processed_segment = await video_service.process_video_segment(
                segment=video_segment,
                audio_file=audio_file,
                output_dir="./data/temp/processing"
            )

            console.print(f"   ✅ 视频片段处理成功")
            console.print(f"     速度比例: {processed_segment.speed_ratio:.3f}")
            console.print(f"     最终路径: {processed_segment.final_segment_path}")

            console.print()
            console.print(Panel.fit(
                "✅ 视频处理服务测试通过！\n模拟的视频调速和音画合成功能正常工作。",
                title="🎉 视频测试结果",
                border_style="green"
            ))

        except Exception as e:
            console.print()
            console.print(Panel.fit(
                f"❌ 视频处理服务测试失败: {str(e)}",
                title="错误",
                border_style="red"
            ))
            sys.exit(1)

    asyncio.run(run_video_test())

@cli.command()
def test_files():
    """测试文件管理服务"""
    import asyncio

    async def run_files_test():
        console.print("📁 测试文件管理服务...", style="yellow")

        try:
            from src.services.file_service import FileManager, ProjectFileManager, CleanupService
            from pathlib import Path

            # 创建文件管理器
            console.print("1. 创建文件管理器...", style="cyan")
            file_manager = FileManager()
            console.print("   ✅ 文件管理器创建成功")

            # 创建项目文件管理器
            console.print("2. 创建项目文件管理器...", style="cyan")
            project_manager = ProjectFileManager("test_project_123")
            console.print("   ✅ 项目文件管理器创建成功")
            console.print(f"     工作空间: {project_manager.workspace['project_temp']}")

            # 创建测试文件并注册
            console.print("3. 测试文件注册...", style="cyan")
            test_file = project_manager.workspace["project_temp"] / "test_file.txt"
            with open(test_file, 'w') as f:
                f.write("This is a test file for file management system.")

            file_id = project_manager.register_file(test_file, "test", {"description": "测试文件"})
            console.print(f"   ✅ 文件注册成功，ID: {file_id}")

            # 更新阶段状态
            console.print("4. 测试阶段状态管理...", style="cyan")
            project_manager.update_stage_status("file_test", "completed", {"files_created": 1})
            stage_status = project_manager.get_stage_status("file_test")
            console.print(f"   ✅ 阶段状态更新成功: {stage_status['status']}")

            # 测试清理服务
            console.print("5. 测试清理服务...", style="cyan")
            cleanup_service = CleanupService()
            disk_usage = cleanup_service.get_disk_usage()
            console.print("   ✅ 磁盘使用情况获取成功")

            for name, usage in disk_usage.items():
                console.print(f"     {name}: {usage['size_mb']:.2f} MB ({usage['file_count']} 文件)")

            console.print()
            console.print(Panel.fit(
                "✅ 文件管理服务测试通过！\n项目工作空间、文件注册和清理功能正常工作。",
                title="🎉 文件管理测试结果",
                border_style="green"
            ))

        except Exception as e:
            console.print()
            console.print(Panel.fit(
                f"❌ 文件管理服务测试失败: {str(e)}",
                title="错误",
                border_style="red"
            ))
            sys.exit(1)

    asyncio.run(run_files_test())

@cli.command()
@click.argument('input_video', required=False, default="test_input.mp4")
@click.option('--style', '-s', default='毒舌电影风格', help='剪辑风格')
@click.option('--language', '-l', default='中文', help='语言')
@click.option('--scenes', '-n', default=3, help='分镜数量')
@click.option('--name', default=None, help='项目名称')
def process(input_video, style, language, scenes, name):
    """处理视频（完整工作流测试）"""
    import asyncio

    async def run_full_workflow():
        console.print("🎬 开始完整视频处理工作流...", style="yellow")

        try:
            from src.workflow.engine import WorkflowEngine
            from pathlib import Path

            # 创建测试输入视频文件
            input_path = Path("./data/input") / input_video
            input_path.parent.mkdir(parents=True, exist_ok=True)
            if not input_path.exists():
                with open(input_path, 'wb') as f:
                    f.write(b"mock_input_video_data_for_testing" * 1000)  # 创建较大的测试文件
                console.print(f"   📁 创建测试输入文件: {input_path}")

            # 创建工作流引擎
            console.print("1. 初始化工作流引擎...", style="cyan")
            engine = WorkflowEngine()

            # 添加进度回调
            def progress_callback(stage, progress, message, project_id):
                if progress >= 0:
                    console.print(f"   📊 [{stage}] {progress:.1%}: {message}")
                else:
                    console.print(f"   ❌ [{stage}] {message}", style="red")

            engine.add_progress_callback(progress_callback)
            console.print("   ✅ 工作流引擎初始化完成")

            # 开始处理
            console.print(f"2. 开始处理视频: {input_video}", style="cyan")
            console.print(f"   风格: {style}")
            console.print(f"   语言: {language}")
            console.print(f"   分镜数: {scenes}")

            project = await engine.process_video(
                input_video_path=str(input_path),
                style=style,
                language=language,
                scene_count=scenes,
                project_name=name or f"Test_{input_video}"
            )

            console.print()
            console.print("🎉 视频处理完成！", style="green")
            console.print(f"   项目ID: {project.id}")
            console.print(f"   项目名称: {project.name}")
            console.print(f"   输出路径: {project.output_video_path}")
            console.print(f"   分镜数量: {len(project.segments)}")
            console.print(f"   处理片段: {len(project.processed_segments)}")

            # 显示项目统计
            if project.segments:
                total_duration = sum(seg.metadata.duration for seg in project.segments)
                console.print(f"   总时长: {total_duration:.2f}秒")

            console.print()
            console.print(Panel.fit(
                f"✅ 完整工作流测试成功！\n"
                f"项目: {project.name}\n"
                f"输出: {project.output_video_path}\n"
                f"状态: {'完成' if project.is_complete else '部分完成'}",
                title="🎬 处理结果",
                border_style="green"
            ))

        except Exception as e:
            console.print()
            console.print(Panel.fit(
                f"❌ 工作流处理失败: {str(e)}",
                title="错误",
                border_style="red"
            ))
            sys.exit(1)

    asyncio.run(run_full_workflow())

if __name__ == '__main__':
    cli()
